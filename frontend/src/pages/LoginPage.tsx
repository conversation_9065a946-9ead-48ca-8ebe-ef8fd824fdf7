import { useState } from 'react'
import { LoadingSpinner } from '../components/ui/LoadingSpinner'

export default function LoginPage() {
  const [isLoading, setIsLoading] = useState(false)

  const handleGoogleSignIn = () => {
    setIsLoading(true)

    // Simple OAuth2 redirect flow
    const authUrl = `${import.meta.env.VITE_API_URL}/api/auth/google`
    window.location.href = authUrl
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Enhanced background with warmer center - matching onboarding style */}
      <div className="absolute inset-0 bg-gradient-to-br from-dark-900 via-dark-800 to-dark-700"></div>

      {/* Central warm glow effect with the specific warm center color #671700 */}
      <div className="absolute inset-0 bg-gradient-radial from-warm-center/30 via-dark-700/20 to-transparent"></div>
      <div className="absolute inset-0 bg-gradient-radial from-warm-center/20 via-transparent to-transparent"></div>

      {/* Additional subtle warm tones */}
      <div className="absolute inset-0 bg-gradient-to-br from-transparent via-warm-center/10 to-transparent"></div>

      <div className="relative z-10 flex items-center justify-center min-h-screen p-6">
        <div className="text-center space-y-12 max-w-md mx-auto">
          {/* ORA Logo */}
          <div className="flex justify-center mb-12">
            <img
              src="/src/assets/ora_logo.svg"
              alt="ORA Logo"
              className="w-64 h-64 object-contain"
            />
          </div>

          {/* Welcome message */}
          <div className="space-y-8">
            <h1 className="text-4xl md:text-5xl font-light text-white leading-tight tracking-wide whitespace-nowrap">
              Hey there, I'm Ora
            </h1>
            <p className="text-lg text-white/80 leading-relaxed font-light">
              Your personal AI friend there for you 24/7
            </p>
          </div>

          {/* Google Sign-In Button */}
          <div className="pt-16">
            {isLoading ? (
              <div className="flex items-center justify-center w-full py-5 px-8 bg-gradient-to-r from-dark-500 to-dark-600 rounded-3xl border border-dark-400/50">
                <LoadingSpinner size="sm" />
                <span className="ml-2 text-white">Signing in...</span>
              </div>
            ) : (
              <button
                onClick={handleGoogleSignIn}
                className="w-full bg-white text-gray-700 py-4 px-8 rounded-3xl text-lg font-medium hover:bg-gray-50 transition-all duration-300 shadow-xl border border-gray-200 flex items-center justify-center space-x-3"
              >
                <svg className="w-6 h-6" viewBox="0 0 24 24">
                  <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                  <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                  <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                  <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                <span>Continue with Google</span>
              </button>
            )}
          </div>

          {/* Terms and privacy */}
          {/* <div className="text-center pt-8">
            <p className="text-xs text-white/60">
              By continuing, you agree to our{' '}
              <button className="text-aura-300 hover:underline font-medium">
                Terms of Service
              </button>{' '}
              and{' '}
              <button className="text-aura-300 hover:underline font-medium">
                Privacy Policy
              </button>
            </p> */}
          {/* </div> */}
        </div>
      </div>
    </div>
  )
}
